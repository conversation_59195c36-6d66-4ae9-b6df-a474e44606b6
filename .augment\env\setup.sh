#!/bin/bash

# Update system packages
sudo apt-get update

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js installation
node --version
npm --version

# Install frontend dependencies
echo "Installing frontend dependencies..."
npm install

# Install backend dependencies
echo "Installing backend dependencies..."
cd backend
npm install
cd ..

# Create basic test configuration for frontend (Vitest)
cat > vitest.config.ts << 'EOF'
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react-swc'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
EOF

# Create test setup file
mkdir -p src/test
cat > src/test/setup.ts << 'EOF'
import '@testing-library/jest-dom'
EOF

# Create a basic component test
mkdir -p src/components/__tests__
cat > src/components/__tests__/Button.test.tsx << 'EOF'
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'

// Simple test component since we don't have a Button component yet
const TestButton = ({ children }: { children: React.ReactNode }) => (
  <button>{children}</button>
)

describe('Button Component', () => {
  it('renders correctly', () => {
    render(<TestButton>Click me</TestButton>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })
})
EOF

# Add test scripts to frontend package.json
npm pkg set scripts.test="vitest"
npm pkg set scripts.test:ui="vitest --ui"
npm pkg set scripts.test:run="vitest run"

# Install vitest as dev dependency
npm install --save-dev vitest @vitest/ui

# Create basic backend test setup
cd backend

# Create basic API test
mkdir -p src/__tests__
cat > src/__tests__/health.test.ts << 'EOF'
import { describe, it, expect } from 'vitest'

describe('Health Check', () => {
  it('should pass basic test', () => {
    expect(1 + 1).toBe(2)
  })
  
  it('should validate environment', () => {
    expect(process.env.NODE_ENV).toBeDefined()
  })
})
EOF

# Add test scripts to backend package.json
npm pkg set scripts.test="vitest"
npm pkg set scripts.test:run="vitest run"

# Install vitest for backend
npm install --save-dev vitest

cd ..

# Add PATH to profile
echo 'export PATH=$PATH:/usr/local/bin' >> $HOME/.profile

echo "✅ Basic testing infrastructure setup complete"
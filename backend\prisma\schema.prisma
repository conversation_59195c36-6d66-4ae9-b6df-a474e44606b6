generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Company {
  id        String   @id @default(cuid())
  name      String
  domain    String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  jobs      Job[]
  users     User[]

  @@map("companies")
}

model User {
  id                String                  @id @default(cuid())
  email             String                  @unique
  passwordHash      String                  @map("password_hash")
  firstName         String?                 @map("first_name")
  lastName          String?                 @map("last_name")
  role              String                  @default("recruiter")
  companyId         String                  @map("company_id")
  avatarUrl         String?                 @map("avatar_url")
  phone             String?
  bio               String?
  createdAt         DateTime                @default(now()) @map("created_at")
  updatedAt         DateTime                @updatedAt @map("updated_at")
  createdForms      ApplicationFormSchema[] @relation("FormCreator")
  reviewedApps      Application[]           @relation("ApplicationReviewer")
  createdInterviews Interview[]             @relation("InterviewCreator")
  createdJobs       Job[]                   @relation("JobCreator")
  notifications     Notification[]
  settings          UserSettings?
  company           Company                 @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("users")
}

model Job {
  id                      String                  @id @default(cuid())
  title                   String
  department              String?
  location                String?
  employmentType          String?                 @map("employment_type")
  experienceLevel         String?                 @map("experience_level")
  salary                  String?
  description             String?
  responsibilities        String?
  requiredQualifications  String?                 @map("required_qualifications")
  preferredQualifications String?                 @map("preferred_qualifications")
  skills                  String?
  benefits                String?
  status                  String                  @default("draft")
  visibility              String                  @default("public")
  postedDate              DateTime?               @map("posted_date")
  applicationDeadline     DateTime?               @map("application_deadline")
  maxApplicants           Int?                    @map("max_applicants")
  currentApplicants       Int                     @default(0) @map("current_applicants")
  pipeline                String?
  source                  String                  @default("internal")
  createdById             String                  @map("created_by_id")
  companyId               String                  @map("company_id")
  createdAt               DateTime                @default(now()) @map("created_at")
  updatedAt               DateTime                @updatedAt @map("updated_at")
  formSchemas             ApplicationFormSchema[]
  applications            Application[]
  company                 Company                 @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdBy               User                    @relation("JobCreator", fields: [createdById], references: [id])

  @@map("jobs")
}

model Candidate {
  id                String           @id @default(cuid())
  firstName         String           @map("first_name")
  lastName          String           @map("last_name")
  email             String           @unique
  phone             String?
  location          String?
  willingToRelocate Boolean?         @map("willing_to_relocate")
  workAuthorization String?          @map("work_authorization")
  linkedinUrl       String?          @map("linkedin_url")
  portfolioUrl      String?          @map("portfolio_url")
  websiteUrl        String?          @map("website_url")
  createdAt         DateTime         @default(now()) @map("created_at")
  updatedAt         DateTime         @updatedAt @map("updated_at")
  sourceId          String?          @map("source_id")
  applications      Application[]
  source            CandidateSource? @relation("CandidateOriginSource", fields: [sourceId], references: [id])

  @@map("candidates")
}

model Application {
  id                   String            @id @default(cuid())
  jobId                String            @map("job_id")
  candidateId          String            @map("candidate_id")
  status               String            @default("applied")
  submittedAt          DateTime?         @map("submitted_at")
  candidateInfo        String            @map("candidate_info")
  professionalInfo     String?           @map("professional_info")
  documentData         String?           @map("document_data")
  customAnswers        String?           @map("custom_answers")
  metadata             String?
  scoring              String?
  activity             String?
  reviewNotes          String?           @map("review_notes")
  reviewedById         String?           @map("reviewed_by_id")
  reviewedAt           DateTime?         @map("reviewed_at")
  tags                 String?
  lastContactDate      DateTime?         @map("last_contact_date")
  nextFollowupDate     DateTime?         @map("next_followup_date")
  communicationHistory String?           @map("communication_history")
  createdAt            DateTime          @default(now()) @map("created_at")
  updatedAt            DateTime          @updatedAt @map("updated_at")
  hiredAt              DateTime?         @map("hired_at")
  score                Int?
  sourceId             String?           @map("source_id")
  candidate            Candidate         @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  job                  Job               @relation(fields: [jobId], references: [id], onDelete: Cascade)
  reviewedBy           User?             @relation("ApplicationReviewer", fields: [reviewedById], references: [id])
  source               CandidateSource?  @relation(fields: [sourceId], references: [id])
  documents            Document[]
  interviews           Interview[]
  mlPredictions        MLPrediction[]
  skillExtractions     SkillExtraction[]

  @@map("applications")
}

model Interview {
  id            String      @id @default(cuid())
  applicationId String      @map("application_id")
  title         String
  type          String?
  scheduledDate DateTime?   @map("scheduled_date")
  startTime     String?     @map("start_time")
  endTime       String?     @map("end_time")
  location      String?
  interviewers  String?
  notes         String?
  feedback      String?
  status        String      @default("scheduled")
  createdById   String      @map("created_by_id")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  createdBy     User        @relation("InterviewCreator", fields: [createdById], references: [id])

  @@map("interviews")
}

model Document {
  id            String      @id @default(cuid())
  applicationId String      @map("application_id")
  filename      String
  fileType      String?     @map("file_type")
  fileSize      Int?        @map("file_size")
  fileUrl       String      @map("file_url")
  documentType  String      @map("document_type")
  uploadedAt    DateTime    @default(now()) @map("uploaded_at")
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("documents")
}

model ApplicationFormSchema {
  id              String    @id @default(cuid())
  jobId           String    @map("job_id")
  title           String
  description     String?
  sections        String
  settings        String
  emailSettings   String    @map("email_settings")
  version         Int       @default(1)
  createdById     String    @map("created_by_id")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  archivedAt      DateTime? @map("archived_at")
  publishedAt     DateTime? @map("published_at")
  status          String    @default("draft")
  submissionCount Int       @default(0) @map("submission_count")
  viewCount       Int       @default(0) @map("view_count")
  createdBy       User      @relation("FormCreator", fields: [createdById], references: [id])
  job             Job       @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@map("application_form_schemas")
}

model EmailTemplate {
  id        String   @id @default(cuid())
  name      String
  subject   String
  body      String
  type      String
  variables String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("email_templates")
}

model CandidateSource {
  id           String        @id @default(cuid())
  name         String        @unique
  category     String
  isActive     Boolean       @default(true) @map("is_active")
  cost         Float?
  description  String?
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  applications Application[]
  candidates   Candidate[]   @relation("CandidateOriginSource")

  @@map("candidate_sources")
}

model MLModel {
  id           String         @id @default(cuid())
  name         String
  type         String
  version      String
  modelPath    String         @map("model_path")
  accuracy     Float?
  precision    Float?
  recall       Float?
  f1Score      Float?         @map("f1_score")
  trainingData String         @map("training_data")
  features     String
  isActive     Boolean        @default(false) @map("is_active")
  trainedAt    DateTime       @map("trained_at")
  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")
  predictions  MLPrediction[]

  @@map("ml_models")
}

model MLPrediction {
  id             String      @id @default(cuid())
  modelId        String      @map("model_id")
  applicationId  String      @map("application_id")
  predictionType String      @map("prediction_type")
  inputFeatures  String      @map("input_features")
  prediction     String
  confidence     Float?
  explanation    String?
  createdAt      DateTime    @default(now()) @map("created_at")
  application    Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  model          MLModel     @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@map("ml_predictions")
}

model TrainingDataset {
  id             String   @id @default(cuid())
  name           String
  description    String?
  source         String
  datasetPath    String   @map("dataset_path")
  features       String
  targetVariable String   @map("target_variable")
  recordCount    Int      @map("record_count")
  version        String   @default("1.0")
  isActive       Boolean  @default(true) @map("is_active")
  metadata       String?
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  @@map("training_datasets")
}

model SkillExtraction {
  id              String      @id @default(cuid())
  applicationId   String      @map("application_id")
  extractedSkills String      @map("extracted_skills")
  skillCategories String      @map("skill_categories")
  experienceLevel String?     @map("experience_level")
  confidence      Float?
  method          String      @default("ml")
  createdAt       DateTime    @default(now()) @map("created_at")
  application     Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("skill_extractions")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  type      String
  title     String
  message   String
  isRead    Boolean  @default(false) @map("is_read")
  metadata  String?
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model UserSettings {
  id                   String   @id @default(cuid())
  userId               String   @unique @map("user_id")
  emailNotifications   Boolean  @default(true) @map("email_notifications")
  pushNotifications    Boolean  @default(true) @map("push_notifications")
  browserNotifications Boolean  @default(true) @map("browser_notifications")
  newApplications      Boolean  @default(true) @map("new_applications")
  interviewReminders   Boolean  @default(true) @map("interview_reminders")
  systemUpdates        Boolean  @default(false) @map("system_updates")
  weeklyReports        Boolean  @default(true) @map("weekly_reports")
  theme                String   @default("light")
  language             String   @default("en")
  timezone             String   @default("UTC")
  profileVisibility    String   @default("team")
  activityTracking     Boolean  @default(true) @map("activity_tracking")
  dataSharing          Boolean  @default(false) @map("data_sharing")
  analyticsOptIn       Boolean  @default(true) @map("analytics_opt_in")
  compactMode          Boolean  @default(false) @map("compact_mode")
  sidebarCollapsed     Boolean  @default(false) @map("sidebar_collapsed")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")
  user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
}
